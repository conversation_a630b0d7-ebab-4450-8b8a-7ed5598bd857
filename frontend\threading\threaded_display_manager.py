"""
Threaded Display Manager for Streamlit Integration.

This module manages the display of results from threaded tab operations
and provides a seamless integration with Streamlit's UI components.
"""

import streamlit as st
import time
from typing import Dict, Any, Optional, List
import threading
from dataclasses import dataclass
from datetime import datetime

from backend.logs.logger_setup import setup_logger
from backend.threading.tab_thread_manager import (
    get_thread_manager, TabType, TabResult,
    submit_summary_task, submit_tod_task, submit_power_analysis_task
)
from backend.threading.tab_executors import (
    SummaryTabExecutor, TodTabExecutor, PowerAnalysisTabExecutor
)

logger = setup_logger('threaded_display_manager', 'threaded_display_manager.log')


@dataclass
class TabDisplayState:
    """Represents the display state of a tab."""
    is_loading: bool = False
    last_update: Optional[datetime] = None
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    task_id: Optional[str] = None


class ThreadedDisplayManager:
    """Manages threaded execution and display of tab content."""
    
    def __init__(self):
        """Initialize the threaded display manager."""
        self.thread_manager = get_thread_manager()
        self.display_states: Dict[TabType, TabDisplayState] = {
            TabType.SUMMARY: TabDisplayState(),
            TabType.TOD: TabDisplayState(),
            TabType.POWER_ANALYSIS: TabDisplayState()
        }
        self._lock = threading.Lock()
        
        logger.info("Threaded display manager initialized")
    
    def execute_summary_tab(self, selected_plant: str, start_date: datetime, 
                           end_date: datetime, is_single_day: bool,
                           has_solar: bool, has_wind: bool, 
                           selected_client: str) -> str:
        """
        Execute Summary tab functions in a separate thread.
        
        Returns:
            str: Task ID for tracking
        """
        with self._lock:
            self.display_states[TabType.SUMMARY].is_loading = True
            self.display_states[TabType.SUMMARY].error = None
        
        # Submit task to Summary thread
        task_id = submit_summary_task(
            SummaryTabExecutor.execute_all_summary_functions,
            selected_plant, start_date, end_date, is_single_day,
            has_solar, has_wind, selected_client,
            priority=10,  # High priority
            timeout=120,  # 2 minutes timeout
            callback=self._summary_callback
        )
        
        with self._lock:
            self.display_states[TabType.SUMMARY].task_id = task_id
        
        logger.info(f"Submitted Summary tab task: {task_id}")
        return task_id
    
    def execute_tod_tab(self, selected_plant: str, start_date: datetime, 
                       end_date: datetime, is_single_day: bool) -> str:
        """
        Execute ToD tab functions in a separate thread.
        
        Returns:
            str: Task ID for tracking
        """
        with self._lock:
            self.display_states[TabType.TOD].is_loading = True
            self.display_states[TabType.TOD].error = None
        
        # Submit task to ToD thread
        task_id = submit_tod_task(
            TodTabExecutor.execute_all_tod_functions,
            selected_plant, start_date, end_date, is_single_day,
            priority=10,  # High priority
            timeout=120,  # 2 minutes timeout
            callback=self._tod_callback
        )
        
        with self._lock:
            self.display_states[TabType.TOD].task_id = task_id
        
        logger.info(f"Submitted ToD tab task: {task_id}")
        return task_id
    
    def execute_power_analysis_tab(self, selected_plant: str, start_date: datetime, 
                                  end_date: datetime, is_single_day: bool) -> str:
        """
        Execute Power Analysis tab functions in a separate thread.
        
        Returns:
            str: Task ID for tracking
        """
        with self._lock:
            self.display_states[TabType.POWER_ANALYSIS].is_loading = True
            self.display_states[TabType.POWER_ANALYSIS].error = None
        
        # Submit task to Power Analysis thread
        task_id = submit_power_analysis_task(
            PowerAnalysisTabExecutor.execute_all_power_analysis_functions,
            selected_plant, start_date, end_date, is_single_day,
            priority=10,  # High priority
            timeout=120,  # 2 minutes timeout
            callback=self._power_analysis_callback
        )
        
        with self._lock:
            self.display_states[TabType.POWER_ANALYSIS].task_id = task_id
        
        logger.info(f"Submitted Power Analysis tab task: {task_id}")
        return task_id
    
    def _summary_callback(self, result: TabResult):
        """Callback for Summary tab results."""
        with self._lock:
            self.display_states[TabType.SUMMARY].is_loading = False
            self.display_states[TabType.SUMMARY].last_update = datetime.now()
            
            if result.success:
                self.display_states[TabType.SUMMARY].results = result.result
                self.display_states[TabType.SUMMARY].error = None
                logger.info(f"Summary tab completed successfully in {result.execution_time:.2f}s")
            else:
                self.display_states[TabType.SUMMARY].error = result.error
                logger.error(f"Summary tab failed: {result.error}")
    
    def _tod_callback(self, result: TabResult):
        """Callback for ToD tab results."""
        with self._lock:
            self.display_states[TabType.TOD].is_loading = False
            self.display_states[TabType.TOD].last_update = datetime.now()
            
            if result.success:
                self.display_states[TabType.TOD].results = result.result
                self.display_states[TabType.TOD].error = None
                logger.info(f"ToD tab completed successfully in {result.execution_time:.2f}s")
            else:
                self.display_states[TabType.TOD].error = result.error
                logger.error(f"ToD tab failed: {result.error}")
    
    def _power_analysis_callback(self, result: TabResult):
        """Callback for Power Analysis tab results."""
        with self._lock:
            self.display_states[TabType.POWER_ANALYSIS].is_loading = False
            self.display_states[TabType.POWER_ANALYSIS].last_update = datetime.now()
            
            if result.success:
                self.display_states[TabType.POWER_ANALYSIS].results = result.result
                self.display_states[TabType.POWER_ANALYSIS].error = None
                logger.info(f"Power Analysis tab completed successfully in {result.execution_time:.2f}s")
            else:
                self.display_states[TabType.POWER_ANALYSIS].error = result.error
                logger.error(f"Power Analysis tab failed: {result.error}")
    
    def display_summary_tab_content(self):
        """Display Summary tab content with threading support."""
        state = self.display_states[TabType.SUMMARY]
        
        if state.is_loading:
            with st.spinner("🔄 Loading Summary data in background thread..."):
                st.info("Summary tab functions are running in a dedicated thread for better performance.")
                
                # Show progress indicator
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                # Wait for results with progress updates
                start_time = time.time()
                while state.is_loading and (time.time() - start_time) < 120:  # 2 minute timeout
                    elapsed = time.time() - start_time
                    progress = min(elapsed / 120, 0.95)  # Max 95% until complete
                    progress_bar.progress(progress)
                    status_text.text(f"Processing... ({elapsed:.1f}s elapsed)")
                    time.sleep(0.5)
                
                progress_bar.progress(1.0)
                status_text.text("Complete!")
        
        # Display results
        if state.error:
            st.error(f"❌ Summary tab error: {state.error}")
        elif state.results:
            self._render_summary_results(state.results)
        elif not state.is_loading:
            st.info("📊 Click 'Refresh Data' to load Summary tab content.")
    
    def display_tod_tab_content(self):
        """Display ToD tab content with threading support."""
        state = self.display_states[TabType.TOD]
        
        if state.is_loading:
            with st.spinner("🔄 Loading ToD data in background thread..."):
                st.info("ToD tab functions are running in a dedicated thread for better performance.")
                
                # Show progress indicator
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                # Wait for results with progress updates
                start_time = time.time()
                while state.is_loading and (time.time() - start_time) < 120:  # 2 minute timeout
                    elapsed = time.time() - start_time
                    progress = min(elapsed / 120, 0.95)  # Max 95% until complete
                    progress_bar.progress(progress)
                    status_text.text(f"Processing... ({elapsed:.1f}s elapsed)")
                    time.sleep(0.5)
                
                progress_bar.progress(1.0)
                status_text.text("Complete!")
        
        # Display results
        if state.error:
            st.error(f"❌ ToD tab error: {state.error}")
        elif state.results:
            self._render_tod_results(state.results)
        elif not state.is_loading:
            st.info("📊 Click 'Refresh Data' to load ToD tab content.")
    
    def display_power_analysis_tab_content(self):
        """Display Power Analysis tab content with threading support."""
        state = self.display_states[TabType.POWER_ANALYSIS]
        
        if state.is_loading:
            with st.spinner("🔄 Loading Power Analysis data in background thread..."):
                st.info("Power Analysis functions are running in a dedicated thread for better performance.")
                
                # Show progress indicator
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                # Wait for results with progress updates
                start_time = time.time()
                while state.is_loading and (time.time() - start_time) < 120:  # 2 minute timeout
                    elapsed = time.time() - start_time
                    progress = min(elapsed / 120, 0.95)  # Max 95% until complete
                    progress_bar.progress(progress)
                    status_text.text(f"Processing... ({elapsed:.1f}s elapsed)")
                    time.sleep(0.5)
                
                progress_bar.progress(1.0)
                status_text.text("Complete!")
        
        # Display results
        if state.error:
            st.error(f"❌ Power Analysis tab error: {state.error}")
        elif state.results:
            self._render_power_analysis_results(state.results)
        elif not state.is_loading:
            st.info("📊 Click 'Refresh Data' to load Power Analysis tab content.")
    
    def _render_summary_results(self, results: Dict[str, Any]):
        """Render Summary tab results."""
        st.success("✅ Summary data loaded successfully!")
        
        # Display Generation vs Consumption
        if 'generation_consumption' in results and 'figure' in results['generation_consumption']:
            st.subheader("📊 Generation vs Consumption")
            st.pyplot(results['generation_consumption']['figure'])
        
        # Display Generation
        if 'generation' in results and 'figure' in results['generation']:
            st.subheader("⚡ Generation")
            st.pyplot(results['generation']['figure'])
        
        # Display Consumption
        if 'consumption' in results and 'figure' in results['consumption']:
            st.subheader("🔌 Consumption")
            st.pyplot(results['consumption']['figure'])
    
    def _render_tod_results(self, results: Dict[str, Any]):
        """Render ToD tab results."""
        st.success("✅ ToD data loaded successfully!")
        
        # Display ToD Generation vs Consumption
        if 'tod_generation_consumption' in results and 'figure' in results['tod_generation_consumption']:
            st.subheader("📊 ToD Generation vs Consumption")
            st.pyplot(results['tod_generation_consumption']['figure'])
        
        # Display ToD Generation
        if 'tod_generation' in results and 'figure' in results['tod_generation']:
            st.subheader("⚡ ToD Generation")
            st.pyplot(results['tod_generation']['figure'])
        
        # Display ToD Consumption
        if 'tod_consumption' in results and 'figure' in results['tod_consumption']:
            st.subheader("🔌 ToD Consumption")
            st.pyplot(results['tod_consumption']['figure'])
    
    def _render_power_analysis_results(self, results: Dict[str, Any]):
        """Render Power Analysis tab results."""
        st.success("✅ Power Analysis data loaded successfully!")
        
        if 'power_cost_analysis' in results:
            st.subheader("💰 Power Cost Analysis")
            st.info("Power cost analysis completed. Results would be displayed here.")
    
    def get_thread_stats(self) -> Dict[str, Any]:
        """Get statistics for all threads."""
        return self.thread_manager.get_all_stats()
    
    def is_any_tab_loading(self) -> bool:
        """Check if any tab is currently loading."""
        return any(state.is_loading for state in self.display_states.values())


# Global threaded display manager instance
_display_manager: Optional[ThreadedDisplayManager] = None

def get_threaded_display_manager() -> ThreadedDisplayManager:
    """Get or create the global threaded display manager."""
    global _display_manager
    if _display_manager is None:
        _display_manager = ThreadedDisplayManager()
    return _display_manager
