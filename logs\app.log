2025-05-27 12:35:06,272 - ERROR - app - app.py:170 - Application error: cannot access local variable 'all_days_df' where it is not associated with a value
2025-05-27 12:35:06,272 - ERROR - app - app.py:171 - Traceback (most recent call last):
  File "D:\<PERSON><PERSON><PERSON><PERSON><PERSON>\Plot Generation\app.py", line 148, in main
    display_tod_generation_view(selected_plant, start_date, end_date, section="tod")
  File "D:\<PERSON><PERSON><PERSON><PERSON>an\Plot Generation\src\display_components.py", line 1458, in display_tod_generation_view
    df=df if is_single_day else all_days_df,
                                ^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'all_days_df' where it is not associated with a value

