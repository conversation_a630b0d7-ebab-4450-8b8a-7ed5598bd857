"""
Async Snowflake database operations.

This module provides async versions of Snowflake database operations
to improve performance by allowing concurrent queries and non-blocking operations.
"""

import asyncio
import concurrent.futures
import pandas as pd
import snowflake.connector
from contextlib import asynccontextmanager
from typing import Optional, Dict, Any, List
import threading
import time
from datetime import datetime

from backend.logs.logger_setup import setup_logger
from db.snow_flake import snowflake_connection, execute_query

logger = setup_logger('async_snowflake', 'async_snowflake.log')


class AsyncSnowflakeConnectionPool:
    """
    Async connection pool for Snowflake database operations.
    
    This class manages a pool of Snowflake connections and provides
    async methods for database operations.
    """
    
    def __init__(self, max_connections: int = 5, max_workers: int = 4):
        """
        Initialize the async connection pool.
        
        Args:
            max_connections: Maximum number of connections in the pool
            max_workers: Maximum number of concurrent workers
        """
        self.max_connections = max_connections
        self.max_workers = max_workers
        self._executor = None
        self._pool_lock = threading.Lock()
        
        logger.info(f"Async Snowflake pool initialized with {max_connections} max connections")
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._executor:
            self._executor.shutdown(wait=True)
    
    async def _run_in_executor(self, func, *args, **kwargs):
        """Run a synchronous function in the thread executor."""
        loop = asyncio.get_event_loop()
        if not self._executor:
            self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        
        try:
            return await loop.run_in_executor(self._executor, func, *args, **kwargs)
        except Exception as e:
            logger.error(f"Executor error in Snowflake operation: {e}")
            raise
    
    async def execute_query_async(self, query: str, params: Optional[tuple] = None,
                                 retries: int = 3, retry_delay: int = 1) -> pd.DataFrame:
        """
        Execute a query asynchronously and return the results as a DataFrame.
        
        Args:
            query: SQL query to execute
            params: Parameters for the query
            retries: Number of retries on failure
            retry_delay: Delay between retries in seconds
            
        Returns:
            DataFrame with query results
        """
        try:
            return await self._run_in_executor(execute_query, query, params, retries, retry_delay)
        except Exception as e:
            logger.error(f"Async query execution failed: {e}")
            return pd.DataFrame()
    
    async def execute_multiple_queries_async(self, queries: List[Dict[str, Any]]) -> Dict[str, pd.DataFrame]:
        """
        Execute multiple queries concurrently.
        
        Args:
            queries: List of dicts with keys: 'query', 'params' (optional), 'key' (for result mapping)
            
        Returns:
            Dictionary mapping query keys to DataFrames
        """
        results = {}
        
        # Create tasks for concurrent execution
        tasks = []
        for query_info in queries:
            query = query_info['query']
            params = query_info.get('params')
            key = query_info.get('key', f"query_{len(tasks)}")
            
            task = self.execute_query_async(query, params)
            tasks.append((key, task))
        
        # Execute all tasks concurrently
        for key, task in tasks:
            try:
                df = await task
                results[key] = df
                logger.info(f"Completed async query: {key}")
            except Exception as e:
                logger.error(f"Failed to execute query {key}: {e}")
                results[key] = pd.DataFrame()
        
        return results
    
    async def get_generation_data_async(self, plant_id: str, start_date: str, 
                                       end_date: str, granularity: str = "15m") -> pd.DataFrame:
        """
        Get generation data from Snowflake asynchronously.
        
        Args:
            plant_id: Plant identifier
            start_date: Start date string (YYYY-MM-DD)
            end_date: End date string (YYYY-MM-DD)
            granularity: Data granularity (15m, 1h, 1d)
            
        Returns:
            DataFrame with generation data
        """
        # This is a placeholder - implement actual Snowflake query based on your schema
        query = """
        SELECT 
            datevalue,
            hour_no,
            plant_generation
        FROM generation_table 
        WHERE plant_id = %s 
        AND datevalue BETWEEN %s AND %s
        ORDER BY datevalue, hour_no
        """
        
        try:
            return await self.execute_query_async(query, (plant_id, start_date, end_date))
        except Exception as e:
            logger.error(f"Failed to get generation data for {plant_id}: {e}")
            return pd.DataFrame()
    
    async def get_consumption_data_async(self, plant_id: str, start_date: str, 
                                        end_date: str) -> pd.DataFrame:
        """
        Get consumption data from Snowflake asynchronously.
        
        Args:
            plant_id: Plant identifier
            start_date: Start date string (YYYY-MM-DD)
            end_date: End date string (YYYY-MM-DD)
            
        Returns:
            DataFrame with consumption data
        """
        # This is a placeholder - implement actual Snowflake query based on your schema
        query = """
        SELECT 
            datevalue,
            hour_no,
            consumption_kwh
        FROM consumption_table 
        WHERE plant_id = %s 
        AND datevalue BETWEEN %s AND %s
        ORDER BY datevalue, hour_no
        """
        
        try:
            return await self.execute_query_async(query, (plant_id, start_date, end_date))
        except Exception as e:
            logger.error(f"Failed to get consumption data for {plant_id}: {e}")
            return pd.DataFrame()
    
    async def get_multiple_plants_data_async(self, plant_requests: List[Dict]) -> Dict[str, Dict[str, pd.DataFrame]]:
        """
        Get data for multiple plants concurrently from Snowflake.
        
        Args:
            plant_requests: List of dicts with keys: plant_id, start_date, end_date, data_types
            
        Returns:
            Nested dictionary: {plant_id: {data_type: DataFrame}}
        """
        results = {}
        
        # Create tasks for concurrent execution
        tasks = []
        for request in plant_requests:
            plant_id = request['plant_id']
            start_date = request['start_date']
            end_date = request['end_date']
            data_types = request.get('data_types', ['generation', 'consumption'])
            
            plant_tasks = {}
            if 'generation' in data_types:
                plant_tasks['generation'] = self.get_generation_data_async(plant_id, start_date, end_date)
            if 'consumption' in data_types:
                plant_tasks['consumption'] = self.get_consumption_data_async(plant_id, start_date, end_date)
            
            tasks.append((plant_id, plant_tasks))
        
        # Execute all tasks concurrently
        for plant_id, plant_tasks in tasks:
            try:
                plant_results = {}
                for data_type, task in plant_tasks.items():
                    df = await task
                    plant_results[data_type] = df
                
                results[plant_id] = plant_results
                logger.info(f"Completed async data fetch for plant {plant_id}")
            except Exception as e:
                logger.error(f"Failed to fetch data for plant {plant_id}: {e}")
                results[plant_id] = {data_type: pd.DataFrame() for data_type in plant_tasks.keys()}
        
        return results
    
    def close(self):
        """Close the executor and clean up resources."""
        if self._executor:
            self._executor.shutdown(wait=True)
            self._executor = None


# Global async connection pool
_async_pool = None

def get_async_snowflake_pool(max_connections: int = 5, max_workers: int = 4) -> AsyncSnowflakeConnectionPool:
    """
    Get or create the global async Snowflake connection pool.
    
    Args:
        max_connections: Maximum number of connections in the pool
        max_workers: Maximum number of concurrent workers
        
    Returns:
        AsyncSnowflakeConnectionPool instance
    """
    global _async_pool
    
    if _async_pool is None:
        _async_pool = AsyncSnowflakeConnectionPool(max_connections, max_workers)
    
    return _async_pool

@asynccontextmanager
async def async_snowflake_pool():
    """Async context manager for Snowflake connection pool."""
    pool = get_async_snowflake_pool()
    async with pool:
        yield pool

async def execute_snowflake_query_async(query: str, params: Optional[tuple] = None) -> pd.DataFrame:
    """
    Convenience function to execute a single Snowflake query asynchronously.
    
    Args:
        query: SQL query to execute
        params: Parameters for the query
        
    Returns:
        DataFrame with query results
    """
    async with async_snowflake_pool() as pool:
        return await pool.execute_query_async(query, params)
