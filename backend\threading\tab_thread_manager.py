"""
Tab Thread Manager for Energy Generation Dashboard.

This module implements a threading architecture where each tab (Summary, ToD, Power Analysis)
runs in its own dedicated thread for improved performance and responsiveness.
"""

import threading
import queue
import time
from typing import Dict, Any, Callable, Optional, List
from dataclasses import dataclass
from enum import Enum
import traceback
from concurrent.futures import ThreadPoolExecutor, Future
import streamlit as st

from backend.logs.logger_setup import setup_logger

logger = setup_logger('tab_thread_manager', 'tab_thread_manager.log')


class TabType(Enum):
    """Enumeration for different tab types."""
    SUMMARY = "summary"
    TOD = "tod"
    POWER_ANALYSIS = "power_analysis"


@dataclass
class TabTask:
    """Represents a task to be executed in a tab thread."""
    task_id: str
    tab_type: TabType
    function: Callable
    args: tuple
    kwargs: dict
    priority: int = 0  # Higher number = higher priority
    timeout: Optional[float] = None
    callback: Optional[Callable] = None


@dataclass
class TabResult:
    """Represents the result of a tab task execution."""
    task_id: str
    tab_type: TabType
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0


class TabThread:
    """Individual thread for handling tab-specific operations."""
    
    def __init__(self, tab_type: TabType, max_queue_size: int = 100):
        """
        Initialize a tab thread.
        
        Args:
            tab_type: Type of tab this thread handles
            max_queue_size: Maximum size of the task queue
        """
        self.tab_type = tab_type
        self.task_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.result_queue = queue.Queue()
        self.thread = None
        self.running = False
        self.stats = {
            'tasks_completed': 0,
            'tasks_failed': 0,
            'total_execution_time': 0.0,
            'average_execution_time': 0.0
        }
        
        logger.info(f"Initialized {tab_type.value} tab thread")
    
    def start(self):
        """Start the tab thread."""
        if self.thread is None or not self.thread.is_alive():
            self.running = True
            self.thread = threading.Thread(target=self._run, daemon=True)
            self.thread.start()
            logger.info(f"Started {self.tab_type.value} tab thread")
    
    def stop(self):
        """Stop the tab thread."""
        self.running = False
        if self.thread and self.thread.is_alive():
            # Add a sentinel task to wake up the thread
            try:
                self.task_queue.put((0, None), timeout=1.0)
            except queue.Full:
                pass
            self.thread.join(timeout=5.0)
            logger.info(f"Stopped {self.tab_type.value} tab thread")
    
    def add_task(self, task: TabTask) -> bool:
        """
        Add a task to the thread's queue.
        
        Args:
            task: Task to be executed
            
        Returns:
            bool: True if task was added successfully, False otherwise
        """
        try:
            # Use negative priority for max-heap behavior (higher priority first)
            priority_item = (-task.priority, task)
            self.task_queue.put(priority_item, timeout=1.0)
            logger.debug(f"Added task {task.task_id} to {self.tab_type.value} thread queue")
            return True
        except queue.Full:
            logger.warning(f"Task queue full for {self.tab_type.value} thread")
            return False
    
    def get_result(self, timeout: Optional[float] = None) -> Optional[TabResult]:
        """
        Get a result from the result queue.
        
        Args:
            timeout: Timeout for getting result
            
        Returns:
            TabResult or None if timeout
        """
        try:
            return self.result_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def _run(self):
        """Main thread execution loop."""
        logger.info(f"Tab thread {self.tab_type.value} started running")
        
        while self.running:
            try:
                # Get task from queue with timeout
                priority_item = self.task_queue.get(timeout=1.0)
                
                if priority_item[1] is None:  # Sentinel value to stop thread
                    break
                
                _, task = priority_item
                self._execute_task(task)
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in {self.tab_type.value} thread: {e}")
                logger.error(traceback.format_exc())
        
        logger.info(f"Tab thread {self.tab_type.value} stopped running")
    
    def _execute_task(self, task: TabTask):
        """
        Execute a single task.
        
        Args:
            task: Task to execute
        """
        start_time = time.time()
        result = TabResult(
            task_id=task.task_id,
            tab_type=self.tab_type,
            success=False
        )
        
        try:
            logger.debug(f"Executing task {task.task_id} in {self.tab_type.value} thread")
            
            # Execute the task function
            if task.timeout:
                # Use ThreadPoolExecutor for timeout support
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(task.function, *task.args, **task.kwargs)
                    result.result = future.result(timeout=task.timeout)
            else:
                result.result = task.function(*task.args, **task.kwargs)
            
            result.success = True
            self.stats['tasks_completed'] += 1
            
            # Execute callback if provided
            if task.callback:
                try:
                    task.callback(result)
                except Exception as e:
                    logger.warning(f"Callback failed for task {task.task_id}: {e}")
            
        except Exception as e:
            result.error = str(e)
            result.success = False
            self.stats['tasks_failed'] += 1
            logger.error(f"Task {task.task_id} failed in {self.tab_type.value} thread: {e}")
            logger.error(traceback.format_exc())
        
        finally:
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            
            # Update statistics
            self.stats['total_execution_time'] += execution_time
            total_tasks = self.stats['tasks_completed'] + self.stats['tasks_failed']
            if total_tasks > 0:
                self.stats['average_execution_time'] = self.stats['total_execution_time'] / total_tasks
            
            # Put result in result queue
            try:
                self.result_queue.put(result, timeout=1.0)
            except queue.Full:
                logger.warning(f"Result queue full for {self.tab_type.value} thread")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get thread statistics."""
        return {
            'tab_type': self.tab_type.value,
            'running': self.running,
            'queue_size': self.task_queue.qsize(),
            'result_queue_size': self.result_queue.qsize(),
            **self.stats
        }


class TabThreadManager:
    """Manager for all tab threads."""
    
    def __init__(self):
        """Initialize the tab thread manager."""
        self.threads: Dict[TabType, TabThread] = {}
        self.task_counter = 0
        self.task_lock = threading.Lock()
        
        # Initialize threads for each tab type
        for tab_type in TabType:
            self.threads[tab_type] = TabThread(tab_type)
        
        logger.info("Tab thread manager initialized")
    
    def start_all_threads(self):
        """Start all tab threads."""
        for thread in self.threads.values():
            thread.start()
        logger.info("All tab threads started")
    
    def stop_all_threads(self):
        """Stop all tab threads."""
        for thread in self.threads.values():
            thread.stop()
        logger.info("All tab threads stopped")
    
    def submit_task(self, tab_type: TabType, function: Callable, *args, 
                   priority: int = 0, timeout: Optional[float] = None,
                   callback: Optional[Callable] = None, **kwargs) -> str:
        """
        Submit a task to the appropriate tab thread.
        
        Args:
            tab_type: Type of tab to submit task to
            function: Function to execute
            *args: Arguments for the function
            priority: Task priority (higher = more important)
            timeout: Execution timeout
            callback: Callback function for result
            **kwargs: Keyword arguments for the function
            
        Returns:
            str: Task ID
        """
        with self.task_lock:
            self.task_counter += 1
            task_id = f"{tab_type.value}_{self.task_counter}_{int(time.time())}"
        
        task = TabTask(
            task_id=task_id,
            tab_type=tab_type,
            function=function,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout,
            callback=callback
        )
        
        if tab_type in self.threads:
            success = self.threads[tab_type].add_task(task)
            if success:
                logger.info(f"Submitted task {task_id} to {tab_type.value} thread")
                return task_id
            else:
                logger.error(f"Failed to submit task {task_id} to {tab_type.value} thread")
                return ""
        else:
            logger.error(f"No thread found for tab type {tab_type.value}")
            return ""
    
    def get_result(self, tab_type: TabType, timeout: Optional[float] = None) -> Optional[TabResult]:
        """
        Get a result from a specific tab thread.
        
        Args:
            tab_type: Type of tab to get result from
            timeout: Timeout for getting result
            
        Returns:
            TabResult or None
        """
        if tab_type in self.threads:
            return self.threads[tab_type].get_result(timeout)
        return None
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all threads."""
        stats = {}
        for tab_type, thread in self.threads.items():
            stats[tab_type.value] = thread.get_stats()
        return stats
    
    def is_thread_running(self, tab_type: TabType) -> bool:
        """Check if a specific thread is running."""
        if tab_type in self.threads:
            return self.threads[tab_type].running
        return False
    
    def restart_thread(self, tab_type: TabType):
        """Restart a specific thread."""
        if tab_type in self.threads:
            self.threads[tab_type].stop()
            time.sleep(0.1)  # Brief pause
            self.threads[tab_type].start()
            logger.info(f"Restarted {tab_type.value} thread")


# Global thread manager instance
_thread_manager: Optional[TabThreadManager] = None

def get_thread_manager() -> TabThreadManager:
    """Get or create the global thread manager."""
    global _thread_manager
    if _thread_manager is None:
        _thread_manager = TabThreadManager()
        _thread_manager.start_all_threads()
    return _thread_manager

def submit_summary_task(function: Callable, *args, priority: int = 0, 
                       timeout: Optional[float] = None, callback: Optional[Callable] = None, 
                       **kwargs) -> str:
    """Submit a task to the Summary tab thread."""
    manager = get_thread_manager()
    return manager.submit_task(TabType.SUMMARY, function, *args, 
                              priority=priority, timeout=timeout, callback=callback, **kwargs)

def submit_tod_task(function: Callable, *args, priority: int = 0, 
                   timeout: Optional[float] = None, callback: Optional[Callable] = None, 
                   **kwargs) -> str:
    """Submit a task to the ToD tab thread."""
    manager = get_thread_manager()
    return manager.submit_task(TabType.TOD, function, *args, 
                              priority=priority, timeout=timeout, callback=callback, **kwargs)

def submit_power_analysis_task(function: Callable, *args, priority: int = 0, 
                              timeout: Optional[float] = None, callback: Optional[Callable] = None, 
                              **kwargs) -> str:
    """Submit a task to the Power Analysis tab thread."""
    manager = get_thread_manager()
    return manager.submit_task(TabType.POWER_ANALYSIS, function, *args, 
                              priority=priority, timeout=timeout, callback=callback, **kwargs)
