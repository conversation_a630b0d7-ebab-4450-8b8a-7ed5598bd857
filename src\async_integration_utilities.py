"""
Async wrapper for Prescinto Integration Utilities.

This module provides async versions of the Prescinto API integration functions
to improve performance by allowing concurrent API calls and non-blocking operations.
"""

import asyncio
import concurrent.futures
import pandas as pd
from typing import Optional, List, Dict, Any
from datetime import datetime
import traceback

from backend.logs.logger_setup import setup_logger
from src.integration_utilities import PrescintoIntegrationUtilities

logger = setup_logger('async_integration', 'async_integration.log')


class AsyncPrescintoIntegration:
    """
    Async wrapper for Prescinto Integration Utilities.
    
    This class provides async methods for all Prescinto API operations,
    allowing for concurrent data fetching and improved performance.
    """
    
    def __init__(self, server: str, token: str, max_workers: int = 4):
        """
        Initialize the async integration wrapper.
        
        Args:
            server: Prescinto server identifier
            token: API authentication token
            max_workers: Maximum number of concurrent workers
        """
        self.server = server
        self.token = token
        self.max_workers = max_workers
        self._integration = None
        self._executor = None
        
        # Initialize the underlying integration
        try:
            self._integration = PrescintoIntegrationUtilities(server=server, token=token)
            logger.info("Async Prescinto integration initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize async Prescinto integration: {e}")
            self._integration = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._executor:
            self._executor.shutdown(wait=True)
    
    async def _run_in_executor(self, func, *args, **kwargs):
        """Run a synchronous function in the thread executor."""
        if not self._integration:
            logger.error("Integration not initialized")
            return None
            
        loop = asyncio.get_event_loop()
        if not self._executor:
            self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        
        try:
            return await loop.run_in_executor(self._executor, func, *args, **kwargs)
        except Exception as e:
            logger.error(f"Executor error: {e}")
            return None
    
    async def fetch_data_v2_async(self, plant_id: str, node_type: str, 
                                 parameters: List[str], node_id: Optional[str] = None,
                                 start_date: str = None, end_date: str = None,
                                 granularity: str = "15m", condition: Dict = None) -> Optional[pd.DataFrame]:
        """
        Async version of fetchDataV2.
        
        Args:
            plant_id: Plant identifier
            node_type: Type of node (Plant, Turbine, etc.)
            parameters: List of parameters to fetch
            node_id: Optional node identifier
            start_date: Start date string (YYYY-MM-DD)
            end_date: End date string (YYYY-MM-DD)
            granularity: Data granularity (15m, 1h, 1d)
            condition: Optional condition dictionary
            
        Returns:
            DataFrame with fetched data or None if failed
        """
        try:
            return await self._run_in_executor(
                self._integration.fetchDataV2,
                plant_id, node_type, parameters, node_id,
                start_date, end_date, granularity, condition
            )
        except Exception as e:
            logger.error(f"Async fetchDataV2 failed for {plant_id}: {e}")
            return None
    
    async def get_generation_data_async(self, plant_id: str, start_date: str, 
                                       end_date: str, granularity: str = "15m",
                                       plant_type: str = "solar") -> Optional[pd.DataFrame]:
        """
        Get generation data asynchronously.
        
        Args:
            plant_id: Plant identifier
            start_date: Start date string (YYYY-MM-DD)
            end_date: End date string (YYYY-MM-DD)
            granularity: Data granularity (15m, 1h, 1d)
            plant_type: Type of plant (solar/wind)
            
        Returns:
            DataFrame with generation data or None if failed
        """
        try:
            if plant_type.lower() == "solar":
                return await self.fetch_data_v2_async(
                    plant_id, "Plant", ["Daily Energy"], None,
                    start_date, end_date, granularity,
                    {"Daily Energy": "last"}
                )
            else:  # wind
                return await self.fetch_data_v2_async(
                    plant_id, "Turbine", ["WTUR.Generation today"], None,
                    start_date, end_date, granularity,
                    {"Generation today": "last"}
                )
        except Exception as e:
            logger.error(f"Async generation data fetch failed for {plant_id}: {e}")
            return None
    
    async def get_multiple_plants_data_async(self, plant_requests: List[Dict]) -> Dict[str, pd.DataFrame]:
        """
        Get data for multiple plants concurrently.
        
        Args:
            plant_requests: List of dicts with keys: plant_id, start_date, end_date, granularity, plant_type
            
        Returns:
            Dictionary mapping plant_id to DataFrame
        """
        results = {}
        
        # Create tasks for concurrent execution
        tasks = []
        for request in plant_requests:
            task = self.get_generation_data_async(
                plant_id=request['plant_id'],
                start_date=request['start_date'],
                end_date=request['end_date'],
                granularity=request.get('granularity', '15m'),
                plant_type=request.get('plant_type', 'solar')
            )
            tasks.append((request['plant_id'], task))
        
        # Execute all tasks concurrently
        for plant_id, task in tasks:
            try:
                df = await task
                results[plant_id] = df if df is not None else pd.DataFrame()
                logger.info(f"Completed async data fetch for {plant_id}")
            except Exception as e:
                logger.error(f"Failed to fetch data for {plant_id}: {e}")
                results[plant_id] = pd.DataFrame()
        
        return results
    
    async def get_plant_structure_async(self, plant_id: str) -> Optional[Dict]:
        """
        Get plant structure asynchronously.
        
        Args:
            plant_id: Plant identifier
            
        Returns:
            Plant structure dictionary or None if failed
        """
        try:
            return await self._run_in_executor(
                self._integration.getStructure, plant_id
            )
        except Exception as e:
            logger.error(f"Async plant structure fetch failed for {plant_id}: {e}")
            return None
    
    async def get_plant_template_async(self, plant_id: str, is_node_id: bool = False) -> Optional[Dict]:
        """
        Get plant template asynchronously.
        
        Args:
            plant_id: Plant identifier
            is_node_id: Whether plant_id is a node ID
            
        Returns:
            Plant template dictionary or None if failed
        """
        try:
            return await self._run_in_executor(
                self._integration.getTemplate, plant_id, is_node_id
            )
        except Exception as e:
            logger.error(f"Async plant template fetch failed for {plant_id}: {e}")
            return None
    
    def close(self):
        """Close the executor and clean up resources."""
        if self._executor:
            self._executor.shutdown(wait=True)
            self._executor = None


# Global async integration instance
_async_integration = None

def get_async_integration(server: str = None, token: str = None) -> Optional[AsyncPrescintoIntegration]:
    """
    Get or create the global async integration instance.
    
    Args:
        server: Prescinto server identifier (optional if already initialized)
        token: API authentication token (optional if already initialized)
        
    Returns:
        AsyncPrescintoIntegration instance or None if failed
    """
    global _async_integration
    
    if _async_integration is None and server and token:
        try:
            _async_integration = AsyncPrescintoIntegration(server, token)
        except Exception as e:
            logger.error(f"Failed to create async integration: {e}")
            return None
    
    return _async_integration

async def fetch_generation_data_async(plant_id: str, start_date: str, end_date: str,
                                     granularity: str = "15m", plant_type: str = "solar") -> Optional[pd.DataFrame]:
    """
    Convenience function to fetch generation data asynchronously.
    
    Args:
        plant_id: Plant identifier
        start_date: Start date string (YYYY-MM-DD)
        end_date: End date string (YYYY-MM-DD)
        granularity: Data granularity (15m, 1h, 1d)
        plant_type: Type of plant (solar/wind)
        
    Returns:
        DataFrame with generation data or None if failed
    """
    integration = get_async_integration()
    if not integration:
        logger.error("Async integration not available")
        return None
    
    return await integration.get_generation_data_async(
        plant_id, start_date, end_date, granularity, plant_type
    )
