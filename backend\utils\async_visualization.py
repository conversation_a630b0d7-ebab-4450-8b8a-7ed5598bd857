"""
Async visualization utilities.

This module provides async versions of visualization functions
to improve performance by allowing concurrent chart generation and non-blocking operations.
"""

import asyncio
import concurrent.futures
import matplotlib.pyplot as plt
import pandas as pd
from typing import Optional, List, Dict, Any, Tuple
import traceback

from backend.logs.logger_setup import setup_logger
from backend.utils.visualization import (
    create_consumption_plot, create_comparison_plot, create_daily_consumption_plot,
    create_daily_comparison_plot, create_combined_wind_solar_plot, create_tod_binned_plot,
    create_daily_tod_binned_plot, create_generation_only_plot, create_tod_generation_plot,
    create_tod_consumption_plot, create_power_cost_comparison_plot, create_power_savings_plot
)

logger = setup_logger('async_visualization', 'async_visualization.log')


class AsyncVisualizationManager:
    """
    Manager for async visualization operations.
    
    This class provides async methods for creating charts and plots,
    allowing for concurrent chart generation and improved performance.
    """
    
    def __init__(self, max_workers: int = 4):
        """
        Initialize the async visualization manager.
        
        Args:
            max_workers: Maximum number of concurrent workers for chart generation
        """
        self.max_workers = max_workers
        self._executor = None
        
        logger.info(f"Async visualization manager initialized with {max_workers} max workers")
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._executor:
            self._executor.shutdown(wait=True)
    
    async def _run_in_executor(self, func, *args, **kwargs):
        """Run a synchronous function in the thread executor."""
        loop = asyncio.get_event_loop()
        if not self._executor:
            self._executor = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        
        try:
            return await loop.run_in_executor(self._executor, func, *args, **kwargs)
        except Exception as e:
            logger.error(f"Executor error in visualization: {e}")
            return None
    
    async def create_consumption_plot_async(self, df: pd.DataFrame, plant_name: str) -> Optional[plt.Figure]:
        """
        Create consumption plot asynchronously.
        
        Args:
            df: Consumption data DataFrame
            plant_name: Name of the plant
            
        Returns:
            Matplotlib figure or None if failed
        """
        try:
            return await self._run_in_executor(create_consumption_plot, df, plant_name)
        except Exception as e:
            logger.error(f"Async consumption plot creation failed: {e}")
            return None
    
    async def create_comparison_plot_async(self, df: pd.DataFrame, plant_name: str, 
                                          selected_date) -> Optional[plt.Figure]:
        """
        Create generation vs consumption comparison plot asynchronously.
        
        Args:
            df: Comparison data DataFrame
            plant_name: Name of the plant
            selected_date: Selected date for the plot
            
        Returns:
            Matplotlib figure or None if failed
        """
        try:
            return await self._run_in_executor(create_comparison_plot, df, plant_name, selected_date)
        except Exception as e:
            logger.error(f"Async comparison plot creation failed: {e}")
            return None
    
    async def create_daily_consumption_plot_async(self, df: pd.DataFrame, plant_name: str,
                                                 start_date, end_date) -> Optional[plt.Figure]:
        """
        Create daily consumption plot asynchronously.
        
        Args:
            df: Daily consumption data DataFrame
            plant_name: Name of the plant
            start_date: Start date
            end_date: End date
            
        Returns:
            Matplotlib figure or None if failed
        """
        try:
            return await self._run_in_executor(create_daily_consumption_plot, df, plant_name, start_date, end_date)
        except Exception as e:
            logger.error(f"Async daily consumption plot creation failed: {e}")
            return None
    
    async def create_daily_comparison_plot_async(self, df: pd.DataFrame, plant_name: str,
                                               start_date, end_date) -> Optional[plt.Figure]:
        """
        Create daily comparison plot asynchronously.
        
        Args:
            df: Daily comparison data DataFrame
            plant_name: Name of the plant
            start_date: Start date
            end_date: End date
            
        Returns:
            Matplotlib figure or None if failed
        """
        try:
            return await self._run_in_executor(create_daily_comparison_plot, df, plant_name, start_date, end_date)
        except Exception as e:
            logger.error(f"Async daily comparison plot creation failed: {e}")
            return None
    
    async def create_generation_only_plot_async(self, df: pd.DataFrame, plant_name: str,
                                              start_date, end_date=None) -> Optional[plt.Figure]:
        """
        Create generation-only plot asynchronously.
        
        Args:
            df: Generation data DataFrame
            plant_name: Name of the plant
            start_date: Start date
            end_date: End date (optional)
            
        Returns:
            Matplotlib figure or None if failed
        """
        try:
            return await self._run_in_executor(create_generation_only_plot, df, plant_name, start_date, end_date)
        except Exception as e:
            logger.error(f"Async generation plot creation failed: {e}")
            return None
    
    async def create_combined_wind_solar_plot_async(self, df: pd.DataFrame, client_name: str,
                                                   start_date, end_date) -> Optional[plt.Figure]:
        """
        Create combined wind and solar plot asynchronously.
        
        Args:
            df: Combined wind and solar data DataFrame
            client_name: Name of the client
            start_date: Start date
            end_date: End date
            
        Returns:
            Matplotlib figure or None if failed
        """
        try:
            return await self._run_in_executor(create_combined_wind_solar_plot, df, client_name, start_date, end_date)
        except Exception as e:
            logger.error(f"Async combined wind/solar plot creation failed: {e}")
            return None
    
    async def create_tod_binned_plot_async(self, df: pd.DataFrame, plant_name: str,
                                          start_date, end_date=None) -> Optional[plt.Figure]:
        """
        Create ToD binned plot asynchronously.
        
        Args:
            df: ToD binned data DataFrame
            plant_name: Name of the plant
            start_date: Start date
            end_date: End date (optional)
            
        Returns:
            Matplotlib figure or None if failed
        """
        try:
            return await self._run_in_executor(create_tod_binned_plot, df, plant_name, start_date, end_date)
        except Exception as e:
            logger.error(f"Async ToD binned plot creation failed: {e}")
            return None
    
    async def create_multiple_plots_async(self, plot_requests: List[Dict[str, Any]]) -> Dict[str, Optional[plt.Figure]]:
        """
        Create multiple plots concurrently.
        
        Args:
            plot_requests: List of dicts with keys: 'plot_type', 'args', 'kwargs', 'key'
            
        Returns:
            Dictionary mapping plot keys to figures
        """
        results = {}
        
        # Create tasks for concurrent execution
        tasks = []
        for request in plot_requests:
            plot_type = request['plot_type']
            args = request.get('args', [])
            kwargs = request.get('kwargs', {})
            key = request.get('key', f"plot_{len(tasks)}")
            
            # Map plot types to async methods
            plot_method_map = {
                'consumption': self.create_consumption_plot_async,
                'comparison': self.create_comparison_plot_async,
                'daily_consumption': self.create_daily_consumption_plot_async,
                'daily_comparison': self.create_daily_comparison_plot_async,
                'generation_only': self.create_generation_only_plot_async,
                'combined_wind_solar': self.create_combined_wind_solar_plot_async,
                'tod_binned': self.create_tod_binned_plot_async,
            }
            
            if plot_type in plot_method_map:
                method = plot_method_map[plot_type]
                task = method(*args, **kwargs)
                tasks.append((key, task))
            else:
                logger.warning(f"Unknown plot type: {plot_type}")
                results[key] = None
        
        # Execute all tasks concurrently
        for key, task in tasks:
            try:
                fig = await task
                results[key] = fig
                logger.info(f"Completed async plot creation: {key}")
            except Exception as e:
                logger.error(f"Failed to create plot {key}: {e}")
                results[key] = None
        
        return results
    
    def close(self):
        """Close the executor and clean up resources."""
        if self._executor:
            self._executor.shutdown(wait=True)
            self._executor = None


# Global async visualization manager
_async_viz_manager = None

def get_async_visualization_manager(max_workers: int = 4) -> AsyncVisualizationManager:
    """
    Get or create the global async visualization manager.
    
    Args:
        max_workers: Maximum number of concurrent workers
        
    Returns:
        AsyncVisualizationManager instance
    """
    global _async_viz_manager
    
    if _async_viz_manager is None:
        _async_viz_manager = AsyncVisualizationManager(max_workers)
    
    return _async_viz_manager

async def create_plot_async(plot_type: str, *args, **kwargs) -> Optional[plt.Figure]:
    """
    Convenience function to create a single plot asynchronously.
    
    Args:
        plot_type: Type of plot to create
        *args: Arguments for the plot function
        **kwargs: Keyword arguments for the plot function
        
    Returns:
        Matplotlib figure or None if failed
    """
    manager = get_async_visualization_manager()
    async with manager:
        plots = await manager.create_multiple_plots_async([{
            'plot_type': plot_type,
            'args': args,
            'kwargs': kwargs,
            'key': 'single_plot'
        }])
        return plots.get('single_plot')
