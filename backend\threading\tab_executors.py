"""
Tab-specific function executors for the Energy Generation Dashboard.

This module contains the main execution functions for each tab that will run
in their dedicated threads.
"""

import streamlit as st
import pandas as pd
from typing import Dict, Any, Optional, Tuple
import traceback
from datetime import datetime

from backend.logs.logger_setup import setup_logger
from backend.threading.tab_thread_manager import TabType

logger = setup_logger('tab_executors', 'tab_executors.log')


class SummaryTabExecutor:
    """Executor for Summary tab functions."""
    
    @staticmethod
    def execute_all_summary_functions(selected_plant: str, start_date: datetime, 
                                    end_date: datetime, is_single_day: bool,
                                    has_solar: bool, has_wind: bool, 
                                    selected_client: str) -> Dict[str, Any]:
        """
        Execute all Summary tab functions.
        
        Args:
            selected_plant: Selected plant name
            start_date: Start date
            end_date: End date
            is_single_day: Whether it's a single day selection
            has_solar: Whether client has solar plants
            has_wind: Whether client has wind plants
            selected_client: Selected client name
            
        Returns:
            Dict containing all results for Summary tab
        """
        logger.info(f"Executing Summary tab functions for {selected_plant}")
        results = {}
        
        try:
            # Import functions here to avoid circular imports
            from src.display_components import (
                display_generation_consumption_view_async,
                display_combined_wind_solar_view,
                display_generation_only_view,
                display_consumption_view_async,
                display_daily_consumption_view_async,
                display_daily_generation_consumption_view
            )
            
            # 1. Generation vs Consumption (1st plot)
            try:
                if is_single_day:
                    gen_cons_result = SummaryTabExecutor._execute_generation_consumption_single(
                        selected_plant, start_date
                    )
                else:
                    gen_cons_result = SummaryTabExecutor._execute_generation_consumption_daily(
                        selected_plant, start_date, end_date
                    )
                results['generation_consumption'] = gen_cons_result
            except Exception as e:
                logger.error(f"Generation vs Consumption failed: {e}")
                results['generation_consumption'] = {'error': str(e)}
            
            # 2. Generation plots (2nd plot)
            try:
                if has_solar and has_wind:
                    # Combined wind and solar
                    gen_result = SummaryTabExecutor._execute_combined_wind_solar(
                        selected_client, start_date, end_date
                    )
                elif selected_plant != "Combined View":
                    # Single plant generation
                    gen_result = SummaryTabExecutor._execute_generation_only(
                        selected_plant, start_date, end_date
                    )
                else:
                    gen_result = {'info': 'Please select a specific plant'}
                
                results['generation'] = gen_result
            except Exception as e:
                logger.error(f"Generation plot failed: {e}")
                results['generation'] = {'error': str(e)}
            
            # 3. Consumption (3rd plot)
            try:
                if is_single_day:
                    cons_result = SummaryTabExecutor._execute_consumption_single(
                        selected_plant, start_date
                    )
                else:
                    cons_result = SummaryTabExecutor._execute_consumption_daily(
                        selected_plant, start_date, end_date
                    )
                results['consumption'] = cons_result
            except Exception as e:
                logger.error(f"Consumption plot failed: {e}")
                results['consumption'] = {'error': str(e)}
            
            logger.info("Summary tab functions completed successfully")
            
        except Exception as e:
            logger.error(f"Summary tab execution failed: {e}")
            logger.error(traceback.format_exc())
            results['error'] = str(e)
        
        return results
    
    @staticmethod
    def _execute_generation_consumption_single(plant_name: str, date: datetime) -> Dict[str, Any]:
        """Execute single day generation vs consumption."""
        from backend.data.data import get_generation_consumption_comparison, compare_generation_consumption
        from backend.utils.visualization import create_comparison_plot
        
        generation_df, consumption_df = get_generation_consumption_comparison(plant_name, date)
        comparison_df = compare_generation_consumption(generation_df, consumption_df)
        
        if not comparison_df.empty:
            fig = create_comparison_plot(comparison_df, plant_name, date)
            return {
                'figure': fig,
                'data': comparison_df,
                'type': 'single_day_comparison'
            }
        return {'error': 'No data available'}
    
    @staticmethod
    def _execute_generation_consumption_daily(plant_name: str, start_date: datetime, 
                                            end_date: datetime) -> Dict[str, Any]:
        """Execute daily generation vs consumption."""
        from backend.data.data import get_daily_generation_consumption_comparison
        from backend.utils.visualization import create_daily_comparison_plot
        
        df = get_daily_generation_consumption_comparison(plant_name, start_date, end_date)
        
        if not df.empty:
            fig = create_daily_comparison_plot(df, plant_name, start_date, end_date)
            return {
                'figure': fig,
                'data': df,
                'type': 'daily_comparison'
            }
        return {'error': 'No data available'}
    
    @staticmethod
    def _execute_combined_wind_solar(client_name: str, start_date: datetime, 
                                   end_date: datetime) -> Dict[str, Any]:
        """Execute combined wind and solar generation."""
        from backend.data.data import get_combined_wind_solar_generation
        from backend.utils.visualization import create_combined_wind_solar_plot
        
        df = get_combined_wind_solar_generation(client_name, start_date, end_date)
        
        if not df.empty:
            fig = create_combined_wind_solar_plot(df, client_name, start_date, end_date)
            return {
                'figure': fig,
                'data': df,
                'type': 'combined_wind_solar'
            }
        return {'error': 'No data available'}
    
    @staticmethod
    def _execute_generation_only(plant_name: str, start_date: datetime, 
                                end_date: datetime) -> Dict[str, Any]:
        """Execute generation only plot."""
        from backend.data.data import get_generation_only_data
        from backend.utils.visualization import create_generation_only_plot
        
        df = get_generation_only_data(plant_name, start_date, end_date)
        
        if not df.empty:
            fig = create_generation_only_plot(df, plant_name, start_date, end_date)
            return {
                'figure': fig,
                'data': df,
                'type': 'generation_only'
            }
        return {'error': 'No data available'}
    
    @staticmethod
    def _execute_consumption_single(plant_name: str, date: datetime) -> Dict[str, Any]:
        """Execute single day consumption."""
        from backend.data.data import get_consumption_data_from_csv
        from backend.utils.visualization import create_consumption_plot
        
        df = get_consumption_data_from_csv(plant_name, date)
        
        if not df.empty:
            fig = create_consumption_plot(df, plant_name)
            return {
                'figure': fig,
                'data': df,
                'type': 'single_day_consumption'
            }
        return {'error': 'No data available'}
    
    @staticmethod
    def _execute_consumption_daily(plant_name: str, start_date: datetime, 
                                 end_date: datetime) -> Dict[str, Any]:
        """Execute daily consumption."""
        from backend.data.data import get_daily_consumption_data
        from backend.utils.visualization import create_daily_consumption_plot
        
        df = get_daily_consumption_data(plant_name, start_date, end_date)
        
        if not df.empty:
            fig = create_daily_consumption_plot(df, plant_name, start_date, end_date)
            return {
                'figure': fig,
                'data': df,
                'type': 'daily_consumption'
            }
        return {'error': 'No data available'}


class TodTabExecutor:
    """Executor for ToD (Time of Day) tab functions."""
    
    @staticmethod
    def execute_all_tod_functions(selected_plant: str, start_date: datetime, 
                                end_date: datetime, is_single_day: bool) -> Dict[str, Any]:
        """
        Execute all ToD tab functions.
        
        Args:
            selected_plant: Selected plant name
            start_date: Start date
            end_date: End date
            is_single_day: Whether it's a single day selection
            
        Returns:
            Dict containing all results for ToD tab
        """
        logger.info(f"Executing ToD tab functions for {selected_plant}")
        results = {}
        
        try:
            # 1. ToD Generation vs Consumption (1st plot)
            try:
                if selected_plant != "Combined View":
                    if is_single_day:
                        tod_comp_result = TodTabExecutor._execute_tod_binned_single(
                            selected_plant, start_date, end_date
                        )
                    else:
                        tod_comp_result = TodTabExecutor._execute_tod_binned_daily(
                            selected_plant, start_date, end_date
                        )
                    results['tod_generation_consumption'] = tod_comp_result
                else:
                    results['tod_generation_consumption'] = {'info': 'Please select a specific plant'}
            except Exception as e:
                logger.error(f"ToD Generation vs Consumption failed: {e}")
                results['tod_generation_consumption'] = {'error': str(e)}
            
            # 2. ToD Generation (2nd plot)
            try:
                if selected_plant != "Combined View":
                    tod_gen_result = TodTabExecutor._execute_tod_generation(
                        selected_plant, start_date, end_date
                    )
                    results['tod_generation'] = tod_gen_result
                else:
                    results['tod_generation'] = {'info': 'Please select a specific plant'}
            except Exception as e:
                logger.error(f"ToD Generation failed: {e}")
                results['tod_generation'] = {'error': str(e)}
            
            # 3. ToD Consumption (3rd plot)
            try:
                if selected_plant != "Combined View":
                    tod_cons_result = TodTabExecutor._execute_tod_consumption(
                        selected_plant, start_date, end_date
                    )
                    results['tod_consumption'] = tod_cons_result
                else:
                    results['tod_consumption'] = {'info': 'Please select a specific plant'}
            except Exception as e:
                logger.error(f"ToD Consumption failed: {e}")
                results['tod_consumption'] = {'error': str(e)}
            
            logger.info("ToD tab functions completed successfully")
            
        except Exception as e:
            logger.error(f"ToD tab execution failed: {e}")
            logger.error(traceback.format_exc())
            results['error'] = str(e)
        
        return results
    
    @staticmethod
    def _execute_tod_binned_single(plant_name: str, start_date: datetime, 
                                 end_date: datetime) -> Dict[str, Any]:
        """Execute single day ToD binned view."""
        from backend.data.data import get_tod_binned_data
        from backend.utils.visualization import create_tod_binned_plot
        
        df = get_tod_binned_data(plant_name, start_date, end_date)
        
        if not df.empty:
            fig = create_tod_binned_plot(df, plant_name, start_date, end_date)
            return {
                'figure': fig,
                'data': df,
                'type': 'tod_binned_single'
            }
        return {'error': 'No data available'}
    
    @staticmethod
    def _execute_tod_binned_daily(plant_name: str, start_date: datetime, 
                                end_date: datetime) -> Dict[str, Any]:
        """Execute daily ToD binned view."""
        from backend.data.data import get_tod_binned_data
        from backend.utils.visualization import create_daily_tod_binned_plot
        
        df = get_tod_binned_data(plant_name, start_date, end_date)
        
        if not df.empty:
            fig = create_daily_tod_binned_plot(df, plant_name, start_date, end_date)
            return {
                'figure': fig,
                'data': df,
                'type': 'tod_binned_daily'
            }
        return {'error': 'No data available'}
    
    @staticmethod
    def _execute_tod_generation(plant_name: str, start_date: datetime, 
                              end_date: datetime) -> Dict[str, Any]:
        """Execute ToD generation view."""
        from backend.data.data import get_tod_binned_data
        from backend.utils.visualization import create_tod_generation_plot
        
        df = get_tod_binned_data(plant_name, start_date, end_date)
        
        if not df.empty:
            fig = create_tod_generation_plot(df, plant_name, start_date, end_date)
            return {
                'figure': fig,
                'data': df,
                'type': 'tod_generation'
            }
        return {'error': 'No data available'}
    
    @staticmethod
    def _execute_tod_consumption(plant_name: str, start_date: datetime, 
                               end_date: datetime) -> Dict[str, Any]:
        """Execute ToD consumption view."""
        from backend.data.data import get_tod_binned_data
        from backend.utils.visualization import create_tod_consumption_plot
        
        df = get_tod_binned_data(plant_name, start_date, end_date)
        
        if not df.empty:
            fig = create_tod_consumption_plot(df, plant_name, start_date, end_date)
            return {
                'figure': fig,
                'data': df,
                'type': 'tod_consumption'
            }
        return {'error': 'No data available'}


class PowerAnalysisTabExecutor:
    """Executor for Power Cost Analysis tab functions."""
    
    @staticmethod
    def execute_all_power_analysis_functions(selected_plant: str, start_date: datetime, 
                                           end_date: datetime, is_single_day: bool) -> Dict[str, Any]:
        """
        Execute all Power Cost Analysis tab functions.
        
        Args:
            selected_plant: Selected plant name
            start_date: Start date
            end_date: End date
            is_single_day: Whether it's a single day selection
            
        Returns:
            Dict containing all results for Power Cost Analysis tab
        """
        logger.info(f"Executing Power Analysis tab functions for {selected_plant}")
        results = {}
        
        try:
            if selected_plant != "Combined View":
                # Execute power cost analysis
                power_analysis_result = PowerAnalysisTabExecutor._execute_power_cost_analysis(
                    selected_plant, start_date, end_date, is_single_day
                )
                results['power_cost_analysis'] = power_analysis_result
            else:
                results['power_cost_analysis'] = {'info': 'Please select a specific plant'}
            
            logger.info("Power Analysis tab functions completed successfully")
            
        except Exception as e:
            logger.error(f"Power Analysis tab execution failed: {e}")
            logger.error(traceback.format_exc())
            results['error'] = str(e)
        
        return results
    
    @staticmethod
    def _execute_power_cost_analysis(plant_name: str, start_date: datetime, 
                                   end_date: datetime, is_single_day: bool) -> Dict[str, Any]:
        """Execute power cost analysis."""
        try:
            from src.display_components import display_power_cost_analysis
            
            # This function typically displays results directly to Streamlit
            # For threading, we need to capture the results
            result = {
                'type': 'power_cost_analysis',
                'plant_name': plant_name,
                'start_date': start_date,
                'end_date': end_date,
                'is_single_day': is_single_day,
                'status': 'completed'
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Power cost analysis failed: {e}")
            return {'error': str(e)}
