"""
Main Streamlit application entry point for the Energy Generation Dashboard.
"""
import streamlit as st
from dotenv import load_dotenv
import traceback
import asyncio
import time

from backend.config.app_config import setup_page
from frontend.components.ui_components import create_client_plant_filters, create_date_filters
from src.display_components import (
    display_consumption_view,
    display_generation_consumption_view, display_daily_generation_consumption_view,
    display_daily_consumption_view,
    display_combined_wind_solar_view,
    display_tod_binned_view,
    display_daily_tod_binned_view, display_tod_generation_view,
    display_tod_consumption_view,
    display_generation_only_view
)
from backend.services.cache_initializer import initialize_cache_system
from backend.logs.error_logger import setup_error_logging
from frontend.threading.threaded_display_manager import get_threaded_display_manager
from backend.threading.tab_thread_manager import get_thread_manager

# Configure logging
from backend.logs.logger_setup import setup_logger

logger = setup_logger('app', 'app.log')

# Load environment variables
load_dotenv()

def measure_performance(func_name: str):
    """Decorator to measure function performance"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            logger.info(f"Performance: {func_name} took {execution_time:.2f} seconds")
            return result
        return wrapper
    return decorator

async def run_async_operations():
    """Run operations that can benefit from async execution"""
    try:
        # Initialize async components
        from backend.utils.async_visualization import get_async_visualization_manager
        from src.async_integration_utilities import get_async_integration
        from backend.config.api_config import get_api_credentials

        # Initialize async integration if credentials are available
        try:
            server, token = get_api_credentials()
            if server and token:
                async_integration = get_async_integration(server, token)
                if async_integration:
                    logger.info("Async integration initialized successfully")
        except Exception as e:
            logger.warning(f"Could not initialize async integration: {e}")

        # Initialize async visualization manager
        viz_manager = get_async_visualization_manager()
        logger.info("Async visualization manager ready")

    except Exception as e:
        logger.error(f"Error initializing async operations: {e}")

def main():
    """Main application function"""
    # Setup page configuration
    setup_page()

    # Setup error logging to capture all errors in error.log
    setup_error_logging()

    # Initialize smart caching system
    initialize_cache_system()

    # Initialize async operations for better performance
    try:
        asyncio.run(run_async_operations())
    except Exception as e:
        logger.warning(f"Could not initialize async operations: {e}")
        # Continue with synchronous operations

    # Initialize threading system for tab-specific operations
    try:
        thread_manager = get_thread_manager()
        display_manager = get_threaded_display_manager()
        logger.info("Threading system initialized successfully")
    except Exception as e:
        logger.warning(f"Could not initialize threading system: {e}")
        thread_manager = None
        display_manager = None

    # Add custom CSS without the header title
    st.markdown("""
    <style>
    .header-container {
        background-color: #f0f2f6;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .header-title {
        color: #1E88E5;
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .header-subtitle {
        color: #424242;
        font-size: 16px;
        margin-bottom: 10px;
    }
    .sidebar-header {
        font-size: 20px;
        font-weight: bold;
        color: #1E88E5;
        margin-bottom: 10px;
    }
    .stSelectbox label {
        font-weight: bold;
        color: #424242;
    }
    </style>
    """, unsafe_allow_html=True)

    try:
        # Create client and plant filters
        filters = create_client_plant_filters()
        selected_client = filters["selected_client"]
        selected_plant = filters["selected_plant"]
        has_solar = filters["has_solar"]
        has_wind = filters["has_wind"]

        # Date filters
        start_date, end_date = create_date_filters()

        # Check if single day is selected
        is_single_day = start_date == end_date

        # Threading Control Panel
        if display_manager:
            st.sidebar.markdown("---")
            st.sidebar.markdown("### 🧵 Threading Control")

            # Show threading status
            thread_stats = display_manager.get_thread_stats()
            is_any_loading = display_manager.is_any_tab_loading()

            if is_any_loading:
                st.sidebar.info("🔄 Background threads are running...")
            else:
                st.sidebar.success("✅ All threads ready")

            # Refresh buttons for each tab
            col1, col2, col3 = st.sidebar.columns(3)

            with col1:
                if st.button("🔄 Summary", help="Refresh Summary tab data in background thread"):
                    if display_manager:
                        display_manager.execute_summary_tab(
                            selected_plant, start_date, end_date, is_single_day,
                            has_solar, has_wind, selected_client
                        )
                        st.sidebar.success("Summary thread started!")

            with col2:
                if st.button("🔄 ToD", help="Refresh ToD tab data in background thread"):
                    if display_manager:
                        display_manager.execute_tod_tab(
                            selected_plant, start_date, end_date, is_single_day
                        )
                        st.sidebar.success("ToD thread started!")

            with col3:
                if st.button("🔄 Power", help="Refresh Power Analysis tab data in background thread"):
                    if display_manager:
                        display_manager.execute_power_analysis_tab(
                            selected_plant, start_date, end_date, is_single_day
                        )
                        st.sidebar.success("Power Analysis thread started!")

            # Show thread statistics in expander
            with st.sidebar.expander("📊 Thread Statistics"):
                for tab_name, stats in thread_stats.items():
                    st.write(f"**{tab_name.title()}:**")
                    st.write(f"- Running: {'✅' if stats['running'] else '❌'}")
                    st.write(f"- Queue Size: {stats['queue_size']}")
                    st.write(f"- Completed: {stats['tasks_completed']}")
                    st.write(f"- Failed: {stats['tasks_failed']}")
                    if stats['average_execution_time'] > 0:
                        st.write(f"- Avg Time: {stats['average_execution_time']:.2f}s")

        # Create tabs for Summary, ToD, and Power Cost Analysis
        summary_tab, tod_tab, cost_tab = st.tabs(["Summary", "ToD", "Power Cost Analysis"])


        # Summary Tab Content
        with summary_tab:
            st.header("📊 Summary Dashboard")

            if display_manager:
                # Use threaded display manager for better performance
                display_manager.display_summary_tab_content()

                # Show performance info
                st.info("💡 **Performance Enhancement**: Summary tab functions run in a dedicated background thread for faster loading and better responsiveness.")
            else:
                # Fallback to original synchronous display
                st.warning("⚠️ Threading system not available. Using synchronous display.")

                # Show Generation vs Consumption based on date selection (1st plot)
                st.subheader("Generation vs Consumption")
                if is_single_day:
                    display_generation_consumption_view(selected_plant, start_date, section="summary")
                else:
                    display_daily_generation_consumption_view(selected_plant, start_date, end_date, section="summary")

                # Show generation plots as 2nd plot based on client configuration and plant selection
                if has_solar and has_wind:
                    # Client has both wind and solar plants
                    st.subheader("Combined Wind and Solar Generation")
                    display_combined_wind_solar_view(selected_client, start_date, end_date, section="summary")
                elif selected_plant != "Combined View":
                    # Client has only one plant OR user selected a specific plant
                    st.subheader("Generation")
                    display_generation_only_view(selected_plant, start_date, end_date, section="summary")
                else:
                    # This case shouldn't occur, but handle it gracefully
                    st.info("Please select a specific plant to view the Generation plot.")

                # Show Consumption based on date selection (3rd plot)
                st.subheader("Consumption")
                if is_single_day:
                    display_consumption_view(selected_plant, start_date, section="summary")
                else:
                    display_daily_consumption_view(selected_plant, start_date, end_date, section="summary")

        # ToD Tab Content
        with tod_tab:
            st.header("⏰ Time of Day Analysis")

            if display_manager:
                # Use threaded display manager for better performance
                display_manager.display_tod_tab_content()

                # Show performance info
                st.info("💡 **Performance Enhancement**: ToD tab functions run in a dedicated background thread for faster loading and better responsiveness.")
            else:
                # Fallback to original synchronous display
                st.warning("⚠️ Threading system not available. Using synchronous display.")

                # Show ToD Generation vs Consumption with custom time bins (as the first plot)
                st.subheader("ToD Generation vs Consumption")

                # Only show the ToD binned view if we have a specific plant selected (not "Combined View")
                if selected_plant != "Combined View":
                    # For single day view
                    if is_single_day:
                        display_tod_binned_view(selected_plant, start_date, end_date, section="tod")
                    # For multi-day view, use the daily ToD binned plot
                    else:
                        display_daily_tod_binned_view(selected_plant, start_date, end_date, section="tod")
                else:
                    st.info("Please select a specific plant to view the ToD Generation vs Consumption comparison.")

                # Show ToD Generation
                st.subheader("ToD Generation")
                if selected_plant != "Combined View":
                    display_tod_generation_view(selected_plant, start_date, end_date, section="tod")
                else:
                    st.info("Please select a specific plant to view the ToD Generation.")

                # Show ToD Consumption
                st.subheader("ToD Consumption")
                if selected_plant != "Combined View":
                    display_tod_consumption_view(selected_plant, start_date, end_date, section="tod")
                else:
                    st.info("Please select a specific plant to view the ToD Consumption.")

        # Power Cost Analysis Tab Content
        with cost_tab:
            st.header("💰 Power Cost Analysis")

            if display_manager:
                # Use threaded display manager for better performance
                display_manager.display_power_analysis_tab_content()

                # Show performance info
                st.info("💡 **Performance Enhancement**: Power Analysis functions run in a dedicated background thread for faster loading and better responsiveness.")
            else:
                # Fallback to original synchronous display
                st.warning("⚠️ Threading system not available. Using synchronous display.")

                if selected_plant != "Combined View":
                    from src.display_components import display_power_cost_analysis
                    display_power_cost_analysis(selected_plant, start_date, end_date, is_single_day)
                else:
                    st.info("Please select a specific plant to view the Power Cost Analysis.")

    except Exception as e:
        logger.error(f"Application error: {e}")
        logger.error(traceback.format_exc())
        st.error("An unexpected error occurred. Please try again later.")
        st.error(f"Error details: {str(e)}")
        st.info("If this problem persists, please contact support.")

if __name__ == "__main__":
    main()
