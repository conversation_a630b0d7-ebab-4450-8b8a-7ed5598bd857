"""
Test script for threading performance improvements.

This script demonstrates the performance benefits of the threading architecture
where each tab runs in its own dedicated thread.
"""

import time
import threading
from datetime import datetime, timedelta
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.logs.logger_setup import setup_logger
from backend.threading.tab_thread_manager import (
    get_thread_manager, TabType,
    submit_summary_task, submit_tod_task, submit_power_analysis_task
)
from backend.threading.tab_executors import (
    SummaryTabExecutor, TodTabExecutor, PowerAnalysisTabExecutor
)
from frontend.threading.threaded_display_manager import get_threaded_display_manager

logger = setup_logger('threading_performance_test', 'threading_performance_test.log')


class ThreadingPerformanceTest:
    """Test class for threading performance evaluation."""
    
    def __init__(self):
        """Initialize the test environment."""
        self.thread_manager = get_thread_manager()
        self.display_manager = get_threaded_display_manager()
        self.test_results = {}
        
        # Test parameters
        self.test_plant = "Test Plant"
        self.test_client = "Test Client"
        self.start_date = datetime.now() - timedelta(days=7)
        self.end_date = datetime.now()
        self.is_single_day = False
        self.has_solar = True
        self.has_wind = False
        
        print("🧵 Threading Performance Test Initialized")
        print(f"   📊 Test Plant: {self.test_plant}")
        print(f"   📅 Date Range: {self.start_date.date()} to {self.end_date.date()}")
    
    def test_sequential_execution(self):
        """Test sequential execution of all tab functions."""
        print("\n📈 Testing Sequential Execution...")
        
        start_time = time.time()
        
        try:
            # Execute Summary tab functions
            print("   🔄 Executing Summary tab...")
            summary_start = time.time()
            summary_result = SummaryTabExecutor.execute_all_summary_functions(
                self.test_plant, self.start_date, self.end_date, self.is_single_day,
                self.has_solar, self.has_wind, self.test_client
            )
            summary_time = time.time() - summary_start
            print(f"   ✅ Summary completed in {summary_time:.2f}s")
            
            # Execute ToD tab functions
            print("   🔄 Executing ToD tab...")
            tod_start = time.time()
            tod_result = TodTabExecutor.execute_all_tod_functions(
                self.test_plant, self.start_date, self.end_date, self.is_single_day
            )
            tod_time = time.time() - tod_start
            print(f"   ✅ ToD completed in {tod_time:.2f}s")
            
            # Execute Power Analysis tab functions
            print("   🔄 Executing Power Analysis tab...")
            power_start = time.time()
            power_result = PowerAnalysisTabExecutor.execute_all_power_analysis_functions(
                self.test_plant, self.start_date, self.end_date, self.is_single_day
            )
            power_time = time.time() - power_start
            print(f"   ✅ Power Analysis completed in {power_time:.2f}s")
            
            total_time = time.time() - start_time
            
            self.test_results['sequential'] = {
                'total_time': total_time,
                'summary_time': summary_time,
                'tod_time': tod_time,
                'power_time': power_time,
                'success': True
            }
            
            print(f"   📊 Sequential Total Time: {total_time:.2f}s")
            
        except Exception as e:
            print(f"   ❌ Sequential execution failed: {e}")
            self.test_results['sequential'] = {'success': False, 'error': str(e)}
    
    def test_threaded_execution(self):
        """Test threaded execution of all tab functions."""
        print("\n🧵 Testing Threaded Execution...")
        
        start_time = time.time()
        
        try:
            # Submit all tasks to their respective threads
            print("   🚀 Submitting tasks to threads...")
            
            summary_task_id = submit_summary_task(
                SummaryTabExecutor.execute_all_summary_functions,
                self.test_plant, self.start_date, self.end_date, self.is_single_day,
                self.has_solar, self.has_wind, self.test_client,
                priority=10
            )
            
            tod_task_id = submit_tod_task(
                TodTabExecutor.execute_all_tod_functions,
                self.test_plant, self.start_date, self.end_date, self.is_single_day,
                priority=10
            )
            
            power_task_id = submit_power_analysis_task(
                PowerAnalysisTabExecutor.execute_all_power_analysis_functions,
                self.test_plant, self.start_date, self.end_date, self.is_single_day,
                priority=10
            )
            
            print(f"   📝 Task IDs: Summary={summary_task_id}, ToD={tod_task_id}, Power={power_task_id}")
            
            # Wait for all tasks to complete
            completed_tasks = 0
            task_times = {}
            timeout = 120  # 2 minutes timeout
            
            while completed_tasks < 3 and (time.time() - start_time) < timeout:
                # Check for Summary results
                if 'summary' not in task_times:
                    result = self.thread_manager.get_result(TabType.SUMMARY, timeout=0.1)
                    if result and result.task_id == summary_task_id:
                        task_times['summary'] = result.execution_time
                        completed_tasks += 1
                        print(f"   ✅ Summary thread completed in {result.execution_time:.2f}s")
                
                # Check for ToD results
                if 'tod' not in task_times:
                    result = self.thread_manager.get_result(TabType.TOD, timeout=0.1)
                    if result and result.task_id == tod_task_id:
                        task_times['tod'] = result.execution_time
                        completed_tasks += 1
                        print(f"   ✅ ToD thread completed in {result.execution_time:.2f}s")
                
                # Check for Power Analysis results
                if 'power' not in task_times:
                    result = self.thread_manager.get_result(TabType.POWER_ANALYSIS, timeout=0.1)
                    if result and result.task_id == power_task_id:
                        task_times['power'] = result.execution_time
                        completed_tasks += 1
                        print(f"   ✅ Power Analysis thread completed in {result.execution_time:.2f}s")
                
                time.sleep(0.1)  # Small delay to prevent busy waiting
            
            total_time = time.time() - start_time
            
            if completed_tasks == 3:
                self.test_results['threaded'] = {
                    'total_time': total_time,
                    'summary_time': task_times.get('summary', 0),
                    'tod_time': task_times.get('tod', 0),
                    'power_time': task_times.get('power', 0),
                    'success': True
                }
                print(f"   📊 Threaded Total Time: {total_time:.2f}s")
            else:
                print(f"   ⚠️ Only {completed_tasks}/3 tasks completed within timeout")
                self.test_results['threaded'] = {
                    'success': False,
                    'error': f'Timeout: only {completed_tasks}/3 tasks completed'
                }
                
        except Exception as e:
            print(f"   ❌ Threaded execution failed: {e}")
            self.test_results['threaded'] = {'success': False, 'error': str(e)}
    
    def test_concurrent_load(self):
        """Test concurrent load with multiple simultaneous requests."""
        print("\n🔀 Testing Concurrent Load...")
        
        start_time = time.time()
        
        try:
            # Submit multiple tasks to each thread
            task_ids = []
            
            for i in range(3):  # 3 tasks per thread
                summary_id = submit_summary_task(
                    SummaryTabExecutor.execute_all_summary_functions,
                    self.test_plant, self.start_date, self.end_date, self.is_single_day,
                    self.has_solar, self.has_wind, self.test_client,
                    priority=5
                )
                task_ids.append(('summary', summary_id))
                
                tod_id = submit_tod_task(
                    TodTabExecutor.execute_all_tod_functions,
                    self.test_plant, self.start_date, self.end_date, self.is_single_day,
                    priority=5
                )
                task_ids.append(('tod', tod_id))
                
                power_id = submit_power_analysis_task(
                    PowerAnalysisTabExecutor.execute_all_power_analysis_functions,
                    self.test_plant, self.start_date, self.end_date, self.is_single_day,
                    priority=5
                )
                task_ids.append(('power', power_id))
            
            print(f"   📝 Submitted {len(task_ids)} concurrent tasks")
            
            # Wait for all tasks to complete
            completed = 0
            timeout = 180  # 3 minutes timeout for concurrent load
            
            while completed < len(task_ids) and (time.time() - start_time) < timeout:
                for tab_type in [TabType.SUMMARY, TabType.TOD, TabType.POWER_ANALYSIS]:
                    result = self.thread_manager.get_result(tab_type, timeout=0.1)
                    if result:
                        completed += 1
                        print(f"   ✅ Task completed: {result.task_id} ({result.execution_time:.2f}s)")
                
                time.sleep(0.1)
            
            total_time = time.time() - start_time
            
            self.test_results['concurrent'] = {
                'total_time': total_time,
                'tasks_submitted': len(task_ids),
                'tasks_completed': completed,
                'success': completed == len(task_ids)
            }
            
            print(f"   📊 Concurrent Load: {completed}/{len(task_ids)} tasks completed in {total_time:.2f}s")
            
        except Exception as e:
            print(f"   ❌ Concurrent load test failed: {e}")
            self.test_results['concurrent'] = {'success': False, 'error': str(e)}
    
    def test_thread_statistics(self):
        """Test thread statistics and monitoring."""
        print("\n📊 Testing Thread Statistics...")
        
        try:
            stats = self.thread_manager.get_all_stats()
            
            print("   📈 Thread Statistics:")
            for tab_name, tab_stats in stats.items():
                print(f"     {tab_name.title()}:")
                print(f"       - Running: {tab_stats['running']}")
                print(f"       - Queue Size: {tab_stats['queue_size']}")
                print(f"       - Tasks Completed: {tab_stats['tasks_completed']}")
                print(f"       - Tasks Failed: {tab_stats['tasks_failed']}")
                print(f"       - Avg Execution Time: {tab_stats['average_execution_time']:.2f}s")
            
            self.test_results['statistics'] = {
                'success': True,
                'stats': stats
            }
            
        except Exception as e:
            print(f"   ❌ Statistics test failed: {e}")
            self.test_results['statistics'] = {'success': False, 'error': str(e)}
    
    def generate_performance_report(self):
        """Generate a comprehensive performance report."""
        print("\n" + "="*60)
        print("📋 THREADING PERFORMANCE REPORT")
        print("="*60)
        
        if 'sequential' in self.test_results and 'threaded' in self.test_results:
            seq = self.test_results['sequential']
            thr = self.test_results['threaded']
            
            if seq['success'] and thr['success']:
                improvement = ((seq['total_time'] - thr['total_time']) / seq['total_time']) * 100
                
                print(f"⏱️  EXECUTION TIME COMPARISON:")
                print(f"   Sequential: {seq['total_time']:.2f}s")
                print(f"   Threaded:   {thr['total_time']:.2f}s")
                print(f"   Improvement: {improvement:.1f}% faster")
                
                print(f"\n📊 INDIVIDUAL TAB TIMES:")
                print(f"   Summary:  {seq['summary_time']:.2f}s → {thr['summary_time']:.2f}s")
                print(f"   ToD:      {seq['tod_time']:.2f}s → {thr['tod_time']:.2f}s")
                print(f"   Power:    {seq['power_time']:.2f}s → {thr['power_time']:.2f}s")
        
        if 'concurrent' in self.test_results:
            conc = self.test_results['concurrent']
            if conc['success']:
                print(f"\n🔀 CONCURRENT LOAD TEST:")
                print(f"   Tasks: {conc['tasks_completed']}/{conc['tasks_submitted']}")
                print(f"   Total Time: {conc['total_time']:.2f}s")
                print(f"   Throughput: {conc['tasks_completed']/conc['total_time']:.2f} tasks/second")
        
        print(f"\n✅ BENEFITS OF THREADING ARCHITECTURE:")
        print(f"   • Each tab runs in its own dedicated thread")
        print(f"   • Non-blocking execution allows parallel processing")
        print(f"   • Better resource utilization and responsiveness")
        print(f"   • Improved user experience with background processing")
        print(f"   • Scalable architecture for concurrent users")
        
        print("\n" + "="*60)


def main():
    """Main function to run threading performance tests."""
    print("🧵 Threading Performance Test Suite")
    print("This script demonstrates the performance benefits of the threading architecture.")
    print("Each tab (Summary, ToD, Power Analysis) runs in its own dedicated thread.\n")
    
    # Initialize test
    test = ThreadingPerformanceTest()
    
    try:
        # Run performance tests
        test.test_sequential_execution()
        test.test_threaded_execution()
        test.test_concurrent_load()
        test.test_thread_statistics()
        
        # Generate report
        test.generate_performance_report()
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        logger.error(f"Threading performance test suite failed: {e}")
    
    finally:
        # Cleanup
        print("\n🧹 Cleaning up threads...")
        test.thread_manager.stop_all_threads()
        print("✅ Cleanup complete!")


if __name__ == "__main__":
    main()
